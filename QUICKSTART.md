# Szybki Start - OHLCV Reader Extension

## Jak przetestować rozszerzenie (bez instalacji Node.js)

1. **Otwórz projekt w VS Code:**
   - Otwórz folder `OhlcvReader` w Visual Studio Code

2. **Uruchom rozszerzenie:**
   - Naciśnij `F5` lub przejdź do `Run > Start Debugging`
   - VS Code otworzy nowe okno "Extension Development Host"

3. **Przetestuj z przykładowymi danymi:**
   - W nowym oknie otwórz plik: `example_data/CzFesN18ZPErgtLoV8CRRDfN7NJW9F7gJxkwXkf68jpA_1m.csv`
   - VS Code zapyta: "How would you like to open this file?"
   - Wybierz **"OHLCV Chart Viewer"**

4. **Zob<PERSON><PERSON>sz:**
   - Interaktywny wykres świecowy (candlestick) z cenami OHLC
   - Wykres wolumenu poniżej
   - Możliwość zoom i pan
   - Tooltips z szczegółami przy najechaniu

## Alternatywne sposoby otwierania

### Metoda 1: <PERSON>u konte<PERSON>
- Kliknij prawym przyciskiem na plik CSV
- Wybierz "Open as OHLCV Chart"

### Metoda 2: Paleta komend
- `Ctrl+Shift+P` (Windows/Linux) lub `Cmd+Shift+P` (Mac)
- Wpisz "Open as OHLCV Chart"
- Wybierz plik CSV

## Funkcje wykresów

- **Zoom:** Kółko myszy lub gestyy touchpad
- **Pan:** Przeciągnij wykres myszą
- **Tooltips:** Najedź na świecę aby zobaczyć:
  - Datę i czas
  - Ceny: Open, High, Low, Close
  - Wolumen (na dolnym wykresie)

## Obsługiwane formaty CSV

Plik musi zawierać kolumny:
- `address` - identyfikator instrumentu
- `o` - cena otwarcia (open)
- `h` - najwyższa cena (high)
- `l` - najniższa cena (low)
- `c` - cena zamknięcia (close)
- `v` - wolumen
- `unixTime` - timestamp (sekundy)
- `type` - interwał czasowy

## Rozwiązywanie problemów

### Rozszerzenie się nie uruchamia
- Sprawdź czy masz VS Code w wersji 1.74+
- Spróbuj zamknąć i ponownie otworzyć VS Code
- Sprawdź konsolę deweloperską: `Help > Toggle Developer Tools`

### Wykres się nie wyświetla
- Sprawdź czy plik CSV ma poprawną strukturę
- Sprawdź czy dane są w poprawnym formacie liczbowym
- Sprawdź konsolę webview w Developer Tools

### Brak danych na wykresie
- Upewnij się że plik zawiera więcej niż jeden wiersz danych
- Sprawdź czy kolumny mają poprawne nazwy
- Sprawdź czy wartości liczbowe nie zawierają błędów

## Instalacja lokalna rozszerzenia

### Metoda 1: Bezpośrednia instalacja (najłatwiejsza)

1. **Otwórz VS Code**
2. **Przejdź do Extensions** (`Ctrl+Shift+X`)
3. **Kliknij "..." (więcej opcji)** w górnym pasku
4. **Wybierz "Install from Folder..."**
5. **Wskaż folder `OhlcvReader`**
6. **Gotowe!** Rozszerzenie będzie dostępne we wszystkich projektach

### Metoda 2: Pakiet .vsix (zalecana)

**Jeśli masz Node.js:**
```bash
# Zainstaluj narzędzie do pakowania
npm install -g vsce

# Stwórz pakiet
vsce package

# Zainstaluj pakiet
code --install-extension ohlcv-reader-0.0.1.vsix
```

**Bez Node.js:**
1. Pobierz `vsce` jako standalone: https://github.com/microsoft/vscode-vsce
2. Lub użyj Metody 1 (bezpośrednia instalacja)

### Metoda 3: Kopiowanie do folderu rozszerzeń

```bash
# Windows
cp -r OhlcvReader %USERPROFILE%\.vscode\extensions\

# macOS/Linux
cp -r OhlcvReader ~/.vscode/extensions/
```

Po instalacji rozszerzenie będzie dostępne we wszystkich projektach VS Code!
