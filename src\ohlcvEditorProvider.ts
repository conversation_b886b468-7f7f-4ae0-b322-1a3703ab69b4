import * as vscode from 'vscode';
import * as path from 'path';

interface OhlcvData {
    address: string;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
    timestamp: number;
    type: string;
}

export class OhlcvEditorProvider implements vscode.CustomTextEditorProvider {
    constructor(private readonly context: vscode.ExtensionContext) {}

    public async resolveCustomTextEditor(
        document: vscode.TextDocument,
        webviewPanel: vscode.WebviewPanel,
        _token: vscode.CancellationToken
    ): Promise<void> {
        // Setup webview options
        webviewPanel.webview.options = {
            enableScripts: true,
        };

        // Set the HTML content
        webviewPanel.webview.html = this.getHtmlForWebview(webviewPanel.webview);

        // Parse CSV data and send to webview
        const csvData = this.parseCsvData(document.getText());
        if (csvData.length > 0) {
            webviewPanel.webview.postMessage({
                type: 'updateData',
                data: csvData
            });
        } else {
            webviewPanel.webview.postMessage({
                type: 'error',
                message: 'Invalid OHLCV data format. Expected columns: address,c,h,l,o,type,unixTime,v'
            });
        }

        // Listen for document changes
        const changeDocumentSubscription = vscode.workspace.onDidChangeTextDocument(e => {
            if (e.document.uri.toString() === document.uri.toString()) {
                const newData = this.parseCsvData(e.document.getText());
                webviewPanel.webview.postMessage({
                    type: 'updateData',
                    data: newData
                });
            }
        });

        webviewPanel.onDidDispose(() => {
            changeDocumentSubscription.dispose();
        });
    }

    private parseCsvData(csvText: string): OhlcvData[] {
        const lines = csvText.trim().split('\n');
        if (lines.length < 2) return [];

        const header = lines[0].toLowerCase();
        
        // Check if this looks like OHLCV data
        const requiredColumns = ['address', 'c', 'h', 'l', 'o', 'type', 'unixtime', 'v'];
        const hasRequiredColumns = requiredColumns.every(col => header.includes(col));
        
        if (!hasRequiredColumns) {
            return [];
        }

        const headerCols = lines[0].split(',');
        const addressIdx = headerCols.findIndex(col => col.toLowerCase() === 'address');
        const closeIdx = headerCols.findIndex(col => col.toLowerCase() === 'c');
        const highIdx = headerCols.findIndex(col => col.toLowerCase() === 'h');
        const lowIdx = headerCols.findIndex(col => col.toLowerCase() === 'l');
        const openIdx = headerCols.findIndex(col => col.toLowerCase() === 'o');
        const typeIdx = headerCols.findIndex(col => col.toLowerCase() === 'type');
        const timeIdx = headerCols.findIndex(col => col.toLowerCase() === 'unixtime');
        const volumeIdx = headerCols.findIndex(col => col.toLowerCase() === 'v');

        const data: OhlcvData[] = [];

        for (let i = 1; i < lines.length; i++) {
            const cols = lines[i].split(',');
            if (cols.length >= headerCols.length) {
                try {
                    data.push({
                        address: cols[addressIdx],
                        open: parseFloat(cols[openIdx]),
                        high: parseFloat(cols[highIdx]),
                        low: parseFloat(cols[lowIdx]),
                        close: parseFloat(cols[closeIdx]),
                        volume: parseFloat(cols[volumeIdx]),
                        timestamp: parseInt(cols[timeIdx]) * 1000, // Convert to milliseconds
                        type: cols[typeIdx]
                    });
                } catch (error) {
                    console.warn(`Failed to parse line ${i}: ${lines[i]}`);
                }
            }
        }

        return data;
    }

    private getHtmlForWebview(webview: vscode.Webview): string {
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'src', 'webview', 'main.js')
        );
        const styleUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'src', 'webview', 'styles.css')
        );

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OHLCV Chart</title>
    <link href="${styleUri}" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial"></script>
</head>
<body>
    <div class="container">
        <div class="chart-container">
            <canvas id="ohlcChart"></canvas>
        </div>
        <div class="volume-container">
            <canvas id="volumeChart"></canvas>
        </div>
        <div id="error-message" class="error-message" style="display: none;"></div>
    </div>
    <script src="${scriptUri}"></script>
</body>
</html>`;
    }
}
