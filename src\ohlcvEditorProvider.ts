import * as vscode from 'vscode';
import * as path from 'path';

interface OhlcvData {
    address: string;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
    timestamp: number;
    type: string;
}

export class OhlcvEditorProvider implements vscode.CustomTextEditorProvider {
    constructor(private readonly context: vscode.ExtensionContext) {}

    public async resolveCustomTextEditor(
        document: vscode.TextDocument,
        webviewPanel: vscode.WebviewPanel,
        _token: vscode.CancellationToken
    ): Promise<void> {
        // Setup webview options
        webviewPanel.webview.options = {
            enableScripts: true,
        };

        // Set the HTML content
        webviewPanel.webview.html = this.getHtmlForWebview(webviewPanel.webview);

        // Parse CSV data and send to webview
        const csvData = this.parseCsvData(document.getText());
        if (csvData.length > 0) {
            webviewPanel.webview.postMessage({
                type: 'updateData',
                data: csvData
            });
        } else {
            webviewPanel.webview.postMessage({
                type: 'error',
                message: 'Invalid OHLCV data format. Expected columns: address,c,h,l,o,type,unixTime,v'
            });
        }

        // Listen for document changes
        const changeDocumentSubscription = vscode.workspace.onDidChangeTextDocument(e => {
            if (e.document.uri.toString() === document.uri.toString()) {
                const newData = this.parseCsvData(e.document.getText());
                webviewPanel.webview.postMessage({
                    type: 'updateData',
                    data: newData
                });
            }
        });

        // Listen for messages from webview
        webviewPanel.webview.onDidReceiveMessage(
            async message => {
                switch (message.type) {
                    case 'cropData':
                        await this.cropDataInFile(document, message.data);
                        break;
                }
            }
        );

        webviewPanel.onDidDispose(() => {
            changeDocumentSubscription.dispose();
        });
    }

    private parseCsvData(csvText: string): OhlcvData[] {
        const lines = csvText.trim().split('\n');
        if (lines.length < 2) return [];

        const header = lines[0].toLowerCase();

        // Check if this looks like OHLCV data
        const requiredColumns = ['address', 'c', 'h', 'l', 'o', 'type', 'unixtime', 'v'];
        const hasRequiredColumns = requiredColumns.every(col => header.includes(col));

        if (!hasRequiredColumns) {
            return [];
        }

        const headerCols = lines[0].split(',');
        const addressIdx = headerCols.findIndex(col => col.toLowerCase() === 'address');
        const closeIdx = headerCols.findIndex(col => col.toLowerCase() === 'c');
        const highIdx = headerCols.findIndex(col => col.toLowerCase() === 'h');
        const lowIdx = headerCols.findIndex(col => col.toLowerCase() === 'l');
        const openIdx = headerCols.findIndex(col => col.toLowerCase() === 'o');
        const typeIdx = headerCols.findIndex(col => col.toLowerCase() === 'type');
        const timeIdx = headerCols.findIndex(col => col.toLowerCase() === 'unixtime');
        const volumeIdx = headerCols.findIndex(col => col.toLowerCase() === 'v');

        const data: OhlcvData[] = [];

        for (let i = 1; i < lines.length; i++) {
            const cols = lines[i].split(',');
            if (cols.length >= headerCols.length) {
                try {
                    data.push({
                        address: cols[addressIdx],
                        open: parseFloat(cols[openIdx]),
                        high: parseFloat(cols[highIdx]),
                        low: parseFloat(cols[lowIdx]),
                        close: parseFloat(cols[closeIdx]),
                        volume: parseFloat(cols[volumeIdx]),
                        timestamp: parseInt(cols[timeIdx]) * 1000, // Convert to milliseconds
                        type: cols[typeIdx]
                    });
                } catch (error) {
                    console.warn(`Failed to parse line ${i}: ${lines[i]}`);
                }
            }
        }

        return data;
    }

    private async cropDataInFile(document: vscode.TextDocument, croppedData: OhlcvData[]): Promise<void> {
        try {
            // Get the original CSV header
            const lines = document.getText().trim().split('\n');
            const header = lines[0];

            // Convert cropped data back to CSV format
            const csvLines = [header];

            for (const item of croppedData) {
                const csvLine = [
                    item.address,
                    item.close.toString(),
                    item.high.toString(),
                    item.low.toString(),
                    item.open.toString(),
                    item.type,
                    Math.floor(item.timestamp / 1000).toString(), // Convert back to seconds
                    item.volume.toString()
                ].join(',');
                csvLines.push(csvLine);
            }

            const newCsvContent = csvLines.join('\n');

            // Create a WorkspaceEdit to replace the entire document content
            const edit = new vscode.WorkspaceEdit();
            const fullRange = new vscode.Range(
                document.positionAt(0),
                document.positionAt(document.getText().length)
            );
            edit.replace(document.uri, fullRange, newCsvContent);

            // Apply the edit
            const success = await vscode.workspace.applyEdit(edit);

            if (success) {
                vscode.window.showInformationMessage(
                    `Data cropped successfully! ${croppedData.length} rows remaining.`
                );
            } else {
                vscode.window.showErrorMessage('Failed to crop data. Please try again.');
            }
        } catch (error) {
            console.error('Error cropping data:', error);
            vscode.window.showErrorMessage('Error cropping data: ' + (error as Error).message);
        }
    }

    private getHtmlForWebview(webview: vscode.Webview): string {
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'src', 'webview', 'main.js')
        );
        const styleUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'src', 'webview', 'styles.css')
        );

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OHLCV Chart</title>
    <link href="${styleUri}" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial"></script>
</head>
<body>
    <div class="container">
        <div class="toolbar">
            <button id="cropButton" class="crop-button" disabled>Crop Selected Data</button>
            <span id="selectionInfo" class="selection-info">Select a range on the chart to crop data</span>
        </div>
        <div class="chart-container">
            <canvas id="ohlcChart"></canvas>
        </div>
        <div class="volume-container">
            <canvas id="volumeChart"></canvas>
        </div>
        <div id="error-message" class="error-message" style="display: none;"></div>
    </div>
    <script src="${scriptUri}"></script>
</body>
</html>`;
    }
}
