"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const ohlcvEditorProvider_1 = require("./ohlcvEditorProvider");
function activate(context) {
    // Register the custom editor provider
    const provider = new ohlcvEditorProvider_1.OhlcvEditorProvider(context);
    const providerRegistration = vscode.window.registerCustomEditorProvider('ohlcvReader.ohlcvEditor', provider, {
        webviewOptions: {
            retainContextWhenHidden: true,
        },
        supportsMultipleEditorsPerDocument: false,
    });
    // Register command to open CSV as chart
    const openAsChartCommand = vscode.commands.registerCommand('ohlcvReader.openAsChart', async (uri) => {
        if (uri) {
            await vscode.commands.executeCommand('vscode.openWith', uri, 'ohlcvReader.ohlcvEditor');
        }
    });
    context.subscriptions.push(providerRegistration, openAsChartCommand);
}
exports.activate = activate;
function deactivate() { }
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map