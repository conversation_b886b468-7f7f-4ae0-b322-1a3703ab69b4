{"version": 3, "file": "ohlcvEditorProvider.js", "sourceRoot": "", "sources": ["../src/ohlcvEditorProvider.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAcjC,MAAa,mBAAmB;IAC5B,YAA6B,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;IAAG,CAAC;IAE1D,KAAK,CAAC,uBAAuB,CAChC,QAA6B,EAC7B,YAAiC,EACjC,MAAgC;QAEhC,wBAAwB;QACxB,YAAY,CAAC,OAAO,CAAC,OAAO,GAAG;YAC3B,aAAa,EAAE,IAAI;SACtB,CAAC;QAEF,uBAAuB;QACvB,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAEzE,qCAAqC;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;QACtD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,OAAO;aAChB,CAAC,CAAC;SACN;aAAM;YACH,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,8EAA8E;aAC1F,CAAC,CAAC;SACN;QAED,8BAA8B;QAC9B,MAAM,0BAA0B,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE;YAC5E,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;gBACvD,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;gBACxD,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC7B,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,OAAO;iBAChB,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,YAAY,CAAC,OAAO,CAAC,mBAAmB,CACpC,KAAK,EAAC,OAAO,EAAC,EAAE;YACZ,QAAQ,OAAO,CAAC,IAAI,EAAE;gBAClB,KAAK,UAAU;oBACX,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClD,MAAM;aACb;QACL,CAAC,CACJ,CAAC;QAEF,YAAY,CAAC,YAAY,CAAC,GAAG,EAAE;YAC3B,0BAA0B,CAAC,OAAO,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,YAAY,CAAC,OAAe;QAChC,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,EAAE,CAAC;QAEhC,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAEtC,sCAAsC;QACtC,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;QACjF,MAAM,kBAAkB,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAE9E,IAAI,CAAC,kBAAkB,EAAE;YACrB,OAAO,EAAE,CAAC;SACb;QAED,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC,CAAC;QAChF,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC;QACxE,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC;QACvE,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC;QACvE,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC;QAC1E,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,CAAC;QAC9E,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC;QAEzE,MAAM,IAAI,GAAgB,EAAE,CAAC;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE;gBAClC,IAAI;oBACA,IAAI,CAAC,IAAI,CAAC;wBACN,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC;wBACzB,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBAC/B,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBAC/B,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC7B,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACjC,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACnC,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI;wBACzC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC;qBACtB,CAAC,CAAC;iBACN;gBAAC,OAAO,KAAK,EAAE;oBACZ,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC1D;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAA6B,EAAE,WAAwB;QAChF,IAAI;YACA,8BAA8B;YAC9B,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAExB,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,CAAC,MAAM,CAAC,CAAC;YAE1B,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;gBAC5B,MAAM,OAAO,GAAG;oBACZ,IAAI,CAAC,OAAO;oBACZ,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;oBACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACpB,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACnB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACpB,IAAI,CAAC,IAAI;oBACT,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE;oBAC5C,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;iBACzB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACZ,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC1B;YAED,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1C,gEAAgE;YAChE,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACxC,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,KAAK,CAC9B,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EACtB,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CACjD,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAErD,iBAAiB;YACjB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAEvD,IAAI,OAAO,EAAE;gBACT,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,8BAA8B,WAAW,CAAC,MAAM,kBAAkB,CACrE,CAAC;aACL;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wCAAwC,CAAC,CAAC;aAC5E;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,GAAI,KAAe,CAAC,OAAO,CAAC,CAAC;SACtF;IACL,CAAC;IAEO,iBAAiB,CAAC,OAAuB;QAC7C,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAClC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAC9E,CAAC;QACF,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CACjC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,CAAC,CACjF,CAAC;QAEF,OAAO;;;;;;kBAMG,QAAQ;;;;;;;;;;;;;;;;;;;mBAmBP,SAAS;;QAEpB,CAAC;IACL,CAAC;CACJ;AAhMD,kDAgMC"}