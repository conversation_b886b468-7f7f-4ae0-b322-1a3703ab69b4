import * as vscode from 'vscode';
import { OhlcvEditorProvider } from './ohlcvEditorProvider';

export function activate(context: vscode.ExtensionContext) {
    // Register the custom editor provider
    const provider = new OhlcvEditorProvider(context);
    const providerRegistration = vscode.window.registerCustomEditorProvider(
        'ohlcvReader.ohlcvEditor',
        provider,
        {
            webviewOptions: {
                retainContextWhenHidden: true,
            },
            supportsMultipleEditorsPerDocument: false,
        }
    );

    // Register command to open CSV as chart
    const openAsChartCommand = vscode.commands.registerCommand(
        'ohlcvReader.openAsChart',
        async (uri: vscode.Uri) => {
            if (uri) {
                await vscode.commands.executeCommand(
                    'vscode.openWith',
                    uri,
                    'ohlcvReader.ohlcvEditor'
                );
            }
        }
    );

    context.subscriptions.push(providerRegistration, openAsChartCommand);
}

export function deactivate() {}
