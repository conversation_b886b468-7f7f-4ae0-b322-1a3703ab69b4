# OHLCV Reader - VS Code Extension

Rozszerzenie Visual Studio Code do wizualizacji danych OHLCV (Open, High, Low, Close, Volume) z plików CSV w formie interaktywnych wykresów świecowych.

## Funkcje

- **Automatyczne wykrywanie** plików CSV z danymi OHLCV
- **Wykresy świecowe (candlestick)** pokazujące ceny OHLC
- **Wykres wolumenu** poniżej głównego wykresu
- **Interaktywność**: zoom, pan, tooltips z szczegółami
- **Responsywny design** dostosowujący się do rozmiaru panelu
- **Integracja z VS Code** - custom editor provider

## Format danych

Rozszerzenie obsługuje pliki CSV z następującymi kolumnami:
- `address` - adres tokena/instrumentu
- `c` - cena <PERSON>am<PERSON> (close)
- `h` - najwy<PERSON><PERSON>a cena (high)
- `l` - najn<PERSON><PERSON><PERSON>a cena (low)
- `o` - cena otwar<PERSON> (open)
- `type` - typ interwału (np. "1m", "5m", "1h")
- `unixTime` - timestamp w formacie Unix (sekundy)
- `v` - wolumen

Przykład:
```csv
address,c,h,l,o,type,unixTime,v
CzFesN18ZPErgtLoV8CRRDfN7NJW9F7gJxkwXkf68jpA,0.00014436989097336774,0.00018644871729154073,0.0001022074974051401,0.00011213613532640381,1m,**********,287674450.45075303
```

## Wymagania

- **Node.js** (wersja 16 lub nowsza) - [Pobierz tutaj](https://nodejs.org/)
- **Visual Studio Code** (wersja 1.74 lub nowsza)

## Instalacja i uruchomienie

1. **Zainstaluj Node.js** (jeśli nie masz):
   - Pobierz z https://nodejs.org/
   - Zainstaluj i uruchom ponownie terminal/VS Code

2. **Zainstaluj zależności:**
   ```bash
   npm install
   ```

3. **Skompiluj rozszerzenie:**
   ```bash
   npm run compile
   ```

4. **Uruchom w trybie deweloperskim:**
   - Otwórz folder w VS Code
   - Naciśnij `F5` aby uruchomić Extension Development Host
   - W nowym oknie VS Code otwórz plik CSV z danymi OHLCV

### ⚡ Szybki test (bez instalacji Node.js)

**Najłatwiejszy sposób na przetestowanie:**

1. **Otwórz folder w VS Code**
2. **Naciśnij `F5`** - uruchomi się Extension Development Host
3. **W nowym oknie otwórz plik CSV:**
   - `example_data/sample_small.csv` (mały przykład)
   - `example_data/CzFesN18ZPErgtLoV8CRRDfN7NJW9F7gJxkwXkf68jpA_1m.csv` (pełne dane)
4. **Wybierz "OHLCV Chart Viewer"** gdy VS Code zapyta o sposób otwarcia

**Gotowe!** Zobaczysz interaktywny wykres świecowy z danymi OHLCV.

## Użycie

### Metoda 1: Automatyczne wykrywanie
1. Otwórz plik CSV z danymi OHLCV
2. VS Code zapyta czy chcesz otworzyć plik jako "OHLCV Chart Viewer"
3. Wybierz "Yes" aby zobaczyć wykres

### Metoda 2: Menu kontekstowe
1. Kliknij prawym przyciskiem na plik CSV w eksploratorze
2. Wybierz "Open as OHLCV Chart" z menu kontekstowego

### Metoda 3: Paleta komend
1. Otwórz paletę komend (`Ctrl+Shift+P`)
2. Wpisz "Open as OHLCV Chart"
3. Wybierz plik CSV

## Funkcje wykresów

- **Zoom**: Użyj kółka myszy lub gestów touchpad
- **Pan**: Przeciągnij wykres myszą
- **Tooltips**: Najedź na świecę aby zobaczyć szczegóły (OHLC, czas)
- **Synchronizacja**: Oba wykresy (OHLC i wolumen) są zsynchronizowane
- **🔥 Cropowanie danych**: Zaznacz zakres i usuń resztę danych z pliku

### 📊 Cropowanie danych (nowa funkcja!)

1. **Kliknij pierwszy punkt** na wykresie - rozpocznie selekcję
2. **Kliknij drugi punkt** - zakończy selekcję zakresu
3. **Naciśnij "Crop Selected Data"** - usunie wszystkie dane poza zaznaczonym zakresem
4. **Oryginalny plik CSV zostanie zaktualizowany** z tylko wybranymi danymi

**Uwaga**: Cropowanie modyfikuje oryginalny plik! Zrób kopię zapasową jeśli potrzebujesz.

## Rozwój

### Struktura projektu
```
├── package.json              # Manifest rozszerzenia
├── tsconfig.json             # Konfiguracja TypeScript
├── src/
│   ├── extension.ts          # Główny plik rozszerzenia
│   ├── ohlcvEditorProvider.ts # Custom editor provider
│   └── webview/
│       ├── main.js           # Logika wykresów (Chart.js)
│       └── styles.css        # Style CSS
└── example_data/             # Przykładowe dane
```

### Technologie
- **TypeScript** - logika rozszerzenia
- **Chart.js** z pluginem finansowym - wykresy świecowe
- **VS Code Extension API** - integracja z edytorem
- **HTML/CSS/JavaScript** - webview

### Kompilacja
```bash
npm run compile    # Jednorazowa kompilacja
npm run watch      # Kompilacja w trybie watch
```

## Licencja

MIT License
