body {
    margin: 0;
    padding: 20px;
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    overflow-x: hidden;
}

.container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.chart-container {
    flex: 3;
    min-height: 400px;
    position: relative;
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 4px;
    padding: 10px;
}

.volume-container {
    flex: 1;
    min-height: 150px;
    position: relative;
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 4px;
    padding: 10px;
}

.error-message {
    background-color: var(--vscode-inputValidation-errorBackground);
    color: var(--vscode-inputValidation-errorForeground);
    border: 1px solid var(--vscode-inputValidation-errorBorder);
    padding: 15px;
    border-radius: 4px;
    margin: 20px 0;
    text-align: center;
}

canvas {
    max-width: 100%;
    height: auto;
}

/* Chart.js tooltip styling */
.chartjs-tooltip {
    background-color: var(--vscode-editorHoverWidget-background) !important;
    color: var(--vscode-editorHoverWidget-foreground) !important;
    border: 1px solid var(--vscode-editorHoverWidget-border) !important;
    border-radius: 4px !important;
    font-family: var(--vscode-font-family) !important;
    font-size: 12px !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
        gap: 10px;
    }
    
    .chart-container {
        min-height: 300px;
    }
    
    .volume-container {
        min-height: 100px;
    }
}
