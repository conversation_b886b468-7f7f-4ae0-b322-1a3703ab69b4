(function() {
    const vscode = acquireVsCodeApi();

    let ohlcChart = null;
    let volumeChart = null;
    let currentData = [];
    let selectedRange = null;

    // Initialize charts
    function initializeCharts() {
        const ohlcCtx = document.getElementById('ohlcChart').getContext('2d');
        const volumeCtx = document.getElementById('volumeChart').getContext('2d');

        // OHLC Chart configuration
        ohlcChart = new Chart(ohlcCtx, {
            type: 'candlestick',
            data: {
                datasets: [{
                    label: 'OHLC',
                    data: [],
                    borderColor: {
                        up: '#26a69a',
                        down: '#ef5350',
                        unchanged: '#999999'
                    },
                    backgroundColor: {
                        up: 'rgba(38, 166, 154, 0.8)',
                        down: 'rgba(239, 83, 80, 0.8)',
                        unchanged: 'rgba(153, 153, 153, 0.8)'
                    }
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: {
                                minute: 'HH:mm',
                                hour: 'HH:mm',
                                day: 'MMM dd'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        }
                    },
                    y: {
                        position: 'right',
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            callback: function(value) {
                                return value.toFixed(8);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.3)',
                        borderWidth: 1,
                        callbacks: {
                            title: function(context) {
                                const date = new Date(context[0].parsed.x);
                                return date.toLocaleString();
                            },
                            label: function(context) {
                                const data = context.raw;
                                return [
                                    `Open: ${data.o.toFixed(8)}`,
                                    `High: ${data.h.toFixed(8)}`,
                                    `Low: ${data.l.toFixed(8)}`,
                                    `Close: ${data.c.toFixed(8)}`
                                ];
                            }
                        }
                    }
                },
                onClick: (event, activeElements, chart) => {
                    console.log('Chart clicked!', activeElements);

                    if (activeElements.length > 0) {
                        // Clicked on a data point
                        const clickedIndex = activeElements[0].index;
                        const clickedTimestamp = currentData[clickedIndex].timestamp;
                        console.log('Clicked on data point:', clickedIndex, clickedTimestamp);
                        handleChartClick(clickedTimestamp);
                    } else {
                        // Clicked on chart area but not on a specific point
                        // Get the x-axis value at click position
                        const canvasPosition = Chart.helpers.getRelativePosition(event, chart);
                        const dataX = chart.scales.x.getValueForPixel(canvasPosition.x);

                        if (dataX && currentData.length > 0) {
                            // Find the closest data point
                            const closestIndex = findClosestDataIndex(dataX);
                            if (closestIndex !== -1) {
                                const clickedTimestamp = currentData[closestIndex].timestamp;
                                console.log('Clicked near data point:', closestIndex, clickedTimestamp);
                                handleChartClick(clickedTimestamp);
                            }
                        }
                    }
                }
            }
        });

        // Volume Chart configuration
        volumeChart = new Chart(volumeCtx, {
            type: 'bar',
            data: {
                datasets: [{
                    label: 'Volume',
                    data: [],
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: {
                                minute: 'HH:mm',
                                hour: 'HH:mm',
                                day: 'MMM dd'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        }
                    },
                    y: {
                        position: 'right',
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.3)',
                        borderWidth: 1,
                        callbacks: {
                            title: function(context) {
                                const date = new Date(context[0].parsed.x);
                                return date.toLocaleString();
                            },
                            label: function(context) {
                                return `Volume: ${context.parsed.y.toLocaleString()}`;
                            }
                        }
                    }
                },
                onClick: (event, activeElements, chart) => {
                    console.log('Volume chart clicked!', activeElements);

                    if (activeElements.length > 0) {
                        // Clicked on a data point
                        const clickedIndex = activeElements[0].index;
                        const clickedTimestamp = currentData[clickedIndex].timestamp;
                        console.log('Clicked on volume data point:', clickedIndex, clickedTimestamp);
                        handleChartClick(clickedTimestamp);
                    } else {
                        // Clicked on chart area but not on a specific point
                        const canvasPosition = Chart.helpers.getRelativePosition(event, chart);
                        const dataX = chart.scales.x.getValueForPixel(canvasPosition.x);

                        if (dataX && currentData.length > 0) {
                            const closestIndex = findClosestDataIndex(dataX);
                            if (closestIndex !== -1) {
                                const clickedTimestamp = currentData[closestIndex].timestamp;
                                console.log('Clicked near volume data point:', closestIndex, clickedTimestamp);
                                handleChartClick(clickedTimestamp);
                            }
                        }
                    }
                }
            }
        });
    }

    // Find closest data point index for a given timestamp
    function findClosestDataIndex(targetTimestamp) {
        if (!currentData || currentData.length === 0) return -1;

        let closestIndex = 0;
        let minDiff = Math.abs(currentData[0].timestamp - targetTimestamp);

        for (let i = 1; i < currentData.length; i++) {
            const diff = Math.abs(currentData[i].timestamp - targetTimestamp);
            if (diff < minDiff) {
                minDiff = diff;
                closestIndex = i;
            }
        }

        return closestIndex;
    }

    // Handle chart click for range selection
    function handleChartClick(timestamp) {
        console.log('handleChartClick called with timestamp:', timestamp);
        if (!selectedRange) {
            // First click - start selection
            selectedRange = { start: timestamp, end: null };
            updateSelectionInfo('Selection started. Click another point to complete range.');
        } else if (!selectedRange.end) {
            // Second click - complete selection
            selectedRange.end = timestamp;

            // Ensure start is before end
            if (selectedRange.start > selectedRange.end) {
                [selectedRange.start, selectedRange.end] = [selectedRange.end, selectedRange.start];
            }

            const startDate = new Date(selectedRange.start).toLocaleString();
            const endDate = new Date(selectedRange.end).toLocaleString();
            updateSelectionInfo(`Selected: ${startDate} to ${endDate}`);

            // Enable crop button
            document.getElementById('cropButton').disabled = false;
        } else {
            // Third click - reset selection
            selectedRange = { start: timestamp, end: null };
            updateSelectionInfo('Selection started. Click another point to complete range.');
            document.getElementById('cropButton').disabled = true;
        }
    }

    // Update selection info text
    function updateSelectionInfo(text) {
        console.log('Selection info updated:', text);
        const selectionElement = document.getElementById('selectionInfo');
        if (selectionElement) {
            selectionElement.textContent = text;
        } else {
            console.error('selectionInfo element not found!');
        }
    }

    // Crop data to selected range
    function cropData() {
        if (!selectedRange || !selectedRange.end || !currentData.length) {
            return;
        }

        const croppedData = currentData.filter(item =>
            item.timestamp >= selectedRange.start && item.timestamp <= selectedRange.end
        );

        if (croppedData.length === 0) {
            showError('No data found in selected range.');
            return;
        }

        // Send crop request to extension
        vscode.postMessage({
            type: 'cropData',
            data: croppedData
        });

        // Reset selection
        selectedRange = null;
        document.getElementById('cropButton').disabled = true;
        updateSelectionInfo('Data cropped! Select a new range if needed.');
    }

    // Update charts with new data
    function updateCharts(data) {
        if (!ohlcChart || !volumeChart) {
            initializeCharts();
        }

        currentData = data;
        console.log('Updating charts with data:', data);

        // Prepare OHLC data for line charts
        const highData = data.map(item => ({
            x: item.timestamp,
            y: item.high
        }));

        const lowData = data.map(item => ({
            x: item.timestamp,
            y: item.low
        }));

        const closeData = data.map(item => ({
            x: item.timestamp,
            y: item.close
        }));

        // Prepare volume data
        const volumeData = data.map(item => ({
            x: item.timestamp,
            y: item.volume
        }));

        console.log('Volume data:', volumeData);

        // Update charts
        ohlcChart.data.datasets[0].data = highData;
        ohlcChart.data.datasets[1].data = lowData;
        ohlcChart.data.datasets[2].data = closeData;
        volumeChart.data.datasets[0].data = volumeData;

        ohlcChart.update();
        volumeChart.update();

        // Hide error message if visible
        document.getElementById('error-message').style.display = 'none';
    }

    // Show error message
    function showError(message) {
        const errorElement = document.getElementById('error-message');
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }

    // Listen for messages from the extension
    window.addEventListener('message', event => {
        const message = event.data;

        switch (message.type) {
            case 'updateData':
                if (message.data && message.data.length > 0) {
                    updateCharts(message.data);
                } else {
                    showError('No valid OHLCV data found in the CSV file.');
                }
                break;
            case 'error':
                showError(message.message);
                break;
        }
    });

    // Initialize charts when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        initializeCharts();

        // Add crop button event listener
        document.getElementById('cropButton').addEventListener('click', cropData);
    });
})();
