(function() {
    const vscode = acquireVsCodeApi();
    
    let ohlcChart = null;
    let volumeChart = null;
    
    // Initialize charts
    function initializeCharts() {
        const ohlcCtx = document.getElementById('ohlcChart').getContext('2d');
        const volumeCtx = document.getElementById('volumeChart').getContext('2d');
        
        // OHLC Chart configuration
        ohlcChart = new Chart(ohlcCtx, {
            type: 'candlestick',
            data: {
                datasets: [{
                    label: 'OHLC',
                    data: [],
                    borderColor: {
                        up: '#26a69a',
                        down: '#ef5350',
                        unchanged: '#999999'
                    },
                    backgroundColor: {
                        up: 'rgba(38, 166, 154, 0.8)',
                        down: 'rgba(239, 83, 80, 0.8)',
                        unchanged: 'rgba(153, 153, 153, 0.8)'
                    }
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: {
                                minute: 'HH:mm',
                                hour: 'HH:mm',
                                day: 'MMM dd'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        }
                    },
                    y: {
                        position: 'right',
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            callback: function(value) {
                                return value.toFixed(8);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.3)',
                        borderWidth: 1,
                        callbacks: {
                            title: function(context) {
                                const date = new Date(context[0].parsed.x);
                                return date.toLocaleString();
                            },
                            label: function(context) {
                                const data = context.raw;
                                return [
                                    `Open: ${data.o.toFixed(8)}`,
                                    `High: ${data.h.toFixed(8)}`,
                                    `Low: ${data.l.toFixed(8)}`,
                                    `Close: ${data.c.toFixed(8)}`
                                ];
                            }
                        }
                    }
                }
            }
        });
        
        // Volume Chart configuration
        volumeChart = new Chart(volumeCtx, {
            type: 'bar',
            data: {
                datasets: [{
                    label: 'Volume',
                    data: [],
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: {
                                minute: 'HH:mm',
                                hour: 'HH:mm',
                                day: 'MMM dd'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        }
                    },
                    y: {
                        position: 'right',
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.3)',
                        borderWidth: 1,
                        callbacks: {
                            title: function(context) {
                                const date = new Date(context[0].parsed.x);
                                return date.toLocaleString();
                            },
                            label: function(context) {
                                return `Volume: ${context.parsed.y.toLocaleString()}`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Update charts with new data
    function updateCharts(data) {
        if (!ohlcChart || !volumeChart) {
            initializeCharts();
        }
        
        // Prepare OHLC data
        const ohlcData = data.map(item => ({
            x: item.timestamp,
            o: item.open,
            h: item.high,
            l: item.low,
            c: item.close
        }));
        
        // Prepare volume data
        const volumeData = data.map(item => ({
            x: item.timestamp,
            y: item.volume
        }));
        
        // Update charts
        ohlcChart.data.datasets[0].data = ohlcData;
        volumeChart.data.datasets[0].data = volumeData;
        
        ohlcChart.update();
        volumeChart.update();
        
        // Hide error message if visible
        document.getElementById('error-message').style.display = 'none';
    }
    
    // Show error message
    function showError(message) {
        const errorElement = document.getElementById('error-message');
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }
    
    // Listen for messages from the extension
    window.addEventListener('message', event => {
        const message = event.data;
        
        switch (message.type) {
            case 'updateData':
                if (message.data && message.data.length > 0) {
                    updateCharts(message.data);
                } else {
                    showError('No valid OHLCV data found in the CSV file.');
                }
                break;
            case 'error':
                showError(message.message);
                break;
        }
    });
    
    // Initialize charts when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        initializeCharts();
    });
})();
