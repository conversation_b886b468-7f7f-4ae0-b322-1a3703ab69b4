{"name": "ohlcv-reader", "displayName": "OHLCV Reader", "description": "Visualize OHLCV (candlestick) data from CSV files", "version": "0.0.1", "publisher": "ohlcv-reader", "engines": {"vscode": "^1.74.0"}, "categories": ["Visualization", "Data Science"], "main": "./out/extension.js", "contributes": {"customEditors": [{"viewType": "ohlcvReader.ohlcvEditor", "displayName": "OHLCV Chart Viewer", "selector": [{"filenamePattern": "*.csv"}], "priority": "option"}], "commands": [{"command": "ohlcvReader.openAsChart", "title": "Open as OHLCV Chart", "category": "OHLCV Reader"}], "menus": {"explorer/context": [{"command": "ohlcvReader.openAsChart", "when": "resourceExtname == .csv", "group": "navigation"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {}}